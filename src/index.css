@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;600&family=Great+Vibes&display=swap');

.guest-form-container {
  background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)),
              url('https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3');
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  padding: 2rem;
  border: 1px solid #e5e5e5;
  margin: 0.5rem;
  box-sizing: border-box;
}

.form-title {
  font-family: 'Great Vibes', cursive;
  font-size: clamp(2rem, 5vw, 2.5rem);
  color: hsl(140, 20%, 45%); /* Sage Green */
  text-align: center;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.input-field {
  width: 100%;
  padding: 0.8rem;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1rem;
  transition: border-color 0.3s;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
}

.input-field:focus {
  outline: none;
  border-color: hsl(140, 20%, 45%); /* Sage Green */
  box-shadow: 0 0 5px rgba(100, 150, 100, 0.2);
}

.submit-button,
.export-button {
  width: 100%;
  padding: 0.875rem;
  background: hsl(140, 20%, 45%); /* Sage Green */
  color: white;
  border: none;
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  -webkit-appearance: none;
  appearance: none;
}

.submit-button:hover,
.export-button:hover {
  background: hsl(140, 25%, 40%); /* Darker Sage Green */
  transform: translateY(-1px);
}

/* Mobile-specific adjustments */
@media (max-width: 600px) {
  .guest-form-container {
    padding: 0.5rem;
  }
  
  .form-card {
    padding: 1.25rem;
    margin: 0.25rem;
    border-radius: 10px;
  }
  
  .input-field {
    padding: 0.7rem;
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
  
  .submit-button,
  .export-button {
    padding: 0.75rem;
    font-size: 1rem;
  }
}

/* Fix for iOS input shadows */
@supports (-webkit-touch-callout: none) {
  .input-field {
    background-color: #ffffff;
  }
}