import { useState, useEffect } from "react";
import { db } from "./firebase";
import { doc, getDoc, setDoc, updateDoc, arrayUnion } from 'firebase/firestore';
import "./GuestForm.css";

export default function GuestForm() {
  const [formData, setFormData] = useState({
    title: "Mr",
    name: "",
    surname: "",
    email: "",
    phone: "",
    behalfOf: "<PERSON><PERSON>",
  });
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Bank account details - commented out for now
  /*
  const bankDetails = `Account name: ONESMUS DZIDZAI MAENZANISE
Account Number: **********
Branch code: 632005
Account Type: Current account
Bank: Absa`;
  */

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);



  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check if user is online before attempting submission
    if (!isOnline) {
      setErrorMessage("You appear to be offline. Please check your internet connection and try again.");
      setShowErrorModal(true);
      return;
    }

    setIsSubmitting(true);

    try {
      const guestListRef = doc(db, "guests", "guestList");
      const guestListDoc = await getDoc(guestListRef);

      // Check guest limits before adding new guest
      let currentGuests = [];
      if (guestListDoc.exists()) {
        currentGuests = guestListDoc.data().guests || [];
      }

      // Count current guests by behalf
      const colletaGuests = currentGuests.filter(guest => guest.behalfOf === "Colleta").length;
      const onesmusGuests = currentGuests.filter(guest => guest.behalfOf === "Onesmus").length;
      const totalGuests = currentGuests.length;

      // Check limits based on who the new guest is for
      if (formData.behalfOf === "Colleta") {
        if (colletaGuests >= 120) {
          setErrorMessage("Sorry, we've reached our guest capacity. Please contact us if you have any questions.");
          setShowErrorModal(true);
          return;
        }
      } else if (formData.behalfOf === "Onesmus") {
        if (onesmusGuests >= 130) {
          setErrorMessage("Sorry, we've reached our guest capacity. Please contact us if you have any questions.");
          setShowErrorModal(true);
          return;
        }
      }

      // Check total guest limit (250 total capacity)
      if (totalGuests >= 250) {
        setErrorMessage("Sorry, we've reached our maximum venue capacity. Please contact us if you have any questions.");
        setShowErrorModal(true);
        return;
      }

      // If we get here, we can add the guest
      if (!guestListDoc.exists()) {
        await setDoc(guestListRef, {
          guests: [formData]
        });
      } else {
        await updateDoc(guestListRef, {
          guests: arrayUnion(formData)
        });
      }

      setShowSuccessModal(true);
      setFormData({ title:"Mr", name: "", surname: "", email: "", phone: "", behalfOf: "Onesmus" });
    } catch (error) {
      console.error("Error submitting form:", error);
      setErrorMessage(
        error.code === 'unavailable' || error.code === 'failed-precondition'
          ? "Unable to connect to our servers. Please check your internet connection and try again."
          : "There was an error submitting your RSVP. Please try again later."
      );
      setShowErrorModal(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
  };

  const closeErrorModal = () => {
    setShowErrorModal(false);
  };

  /*
  const copyBankDetails = () => {
    navigator.clipboard.writeText(bankDetails).then(() => {
      setCopySuccess(true);
      // Reset copy success message after 2 seconds
      setTimeout(() => setCopySuccess(false), 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };
  */

  const weddingDate = new Date("2025-08-23");
  const today = new Date();
  const daysLeft = Math.ceil((weddingDate - today) / (1000 * 60 * 60 * 24));
  
  return (
    <div className="guest-form-container">
      <div className="form-card">
        <h1 className="welcome-title">You're Invited!</h1>
        <h2 className="couple-names">Onesmus & Colleta</h2>
        <div className="wedding-date">August 23, 2025</div>
        <div className="wedding-date">Cape Town, South Africa</div>

        <div className="countdown-banner">
          <span className="days-counter">{daysLeft}</span> days to go!
        </div>
        
        <div className="welcome-message">
          <p>Welcome to our wedding registry! You have been officially invited to celebrate our special day with us.</p>
          <p>Please provide your details below so that in the coming months, we can add you to our wedding webapp where you'll find all the information about our celebration.</p>
        </div>

        <h3 className="form-subtitle">Guest Registration</h3>
        
        <form onSubmit={handleSubmit}>
        <select 
            name="title" 
            value={formData.title} 
            onChange={handleChange} 
            className="input-field"
          >
            <option value="Mr">Mr</option>
            <option value="Mrs">Mrs</option>
            <option value="Ms">Ms</option>
          </select>
          <input 
            name="name" 
            placeholder="Name" 
            value={formData.name} 
            onChange={handleChange} 
            className="input-field" 
            required 
          />
          <input 
            name="surname" 
            placeholder="Surname" 
            value={formData.surname} 
            onChange={handleChange} 
            className="input-field" 
            required 
          />
          <input 
            type="email" 
            name="email" 
            placeholder="Email" 
            value={formData.email} 
            onChange={handleChange} 
            className="input-field" 
            required 
          />
          <input 
            type="tel" 
            name="phone" 
            placeholder="Phone Number" 
            value={formData.phone} 
            onChange={handleChange} 
            className="input-field" 
            required 
          />
          <select 
            name="behalfOf" 
            value={formData.behalfOf} 
            onChange={handleChange} 
            className="input-field"
          >
            <option value="Onesmus">Onesmus</option>
            <option value="Colleta">Colleta</option>
          </select>
          <button 
            type="submit" 
            className="submit-button" 
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "RSVP"}
          </button>
        </form>

        {/* Bank Details Section - commented out for now
        <div className="section-divider"></div>

        <div className="bank-details-section">
          <h3 className="support-title">Support Our Journey</h3>
          <p className="support-message">If you'd like to contribute to our new chapter, we'd be honored by your support.</p>
          <div className="bank-details-card">
            <pre className="bank-details-text">{bankDetails}</pre>
            <button 
              onClick={copyBankDetails} 
              className="copy-button"
            >
              {copySuccess ? "Copied!" : "Copy to Clipboard"}
            </button>
          </div>
        </div>
        */}
        
        <div className="thank-you-message">
          We look forward to celebrating with you!
        </div>
        
        {!isOnline && (
          <div className="offline-notice">
            You appear to be offline. Please connect to the internet to submit your RSVP.
          </div>
        )}
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="modal-overlay" onClick={closeSuccessModal}>
          <div className="success-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-content">
              <div className="modal-icon">✓</div>
              <h3 className="modal-title">Thank You!</h3>
              <p className="modal-message">Your RSVP has been received.</p>
              <p className="modal-message">We can't wait to celebrate our special day with you!</p>
              
              {/* Bank account details - commented out for now
              <div className="bank-details">
                <h4>You can choose to support us by sending funds to:</h4>
                <div className="bank-details-card">
                  <pre className="bank-details-text">{bankDetails}</pre>
                  <button 
                    onClick={copyBankDetails} 
                    className="copy-button"
                  >
                    {copySuccess ? "Copied!" : "Copy to Clipboard"}
                  </button>
                </div>
              </div>
              */}
              
              <button onClick={closeSuccessModal} className="modal-button">Close</button>
            </div>
          </div>
        </div>
      )}

      {/* Error Modal */}
      {showErrorModal && (
        <div className="modal-overlay" onClick={closeErrorModal}>
          <div className="error-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-content">
              <div className="modal-icon error">!</div>
              <h3 className="modal-title">Oops!</h3>
              <p className="modal-message">{errorMessage}</p>
              <button onClick={closeErrorModal} className="modal-button">Close</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
