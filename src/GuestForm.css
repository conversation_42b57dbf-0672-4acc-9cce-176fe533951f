@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;600&family=Great+Vibes&family=Playfair+Display:wght@400;700&display=swap');

.guest-form-container {
  background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)),
              url('https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3');
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-sizing: border-box;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  padding: 2rem;
  border: 1px solid #d1d9c3; /* Light sage green */
  margin: 0.5rem;
  box-sizing: border-box;
  text-align: center;
}

.welcome-title {
  font-family: 'Great Vibes', cursive;
  font-size: clamp(2.5rem, 6vw, 3.5rem);
  color: #4a6741; /* Sage green */
  text-align: center;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.couple-names {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: clamp(1.8rem, 4vw, 2.3rem);
  color: #3d5635; /* Darker sage green */
  margin-bottom: 0.5rem;
}

.wedding-date {
  font-size: 1.2rem;
  color: #4a6741;
  font-weight: 600;
  font-family: 'Cormorant Garamond', serif;
  margin-bottom: 0.5rem;
}

.countdown-banner {
  background-color: #f1f5eb;
  border: 1px dashed #a3b18a;
  border-radius: 8px;
  padding: 0.7rem;
  margin: 1rem 0;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  color: #4a6741;
}

.days-counter {
  font-weight: 700;
  font-size: 1.3rem;
  color: #3d5635;
}

.welcome-message {
  font-family: 'Cormorant Garamond', serif;
  color: #555;
  margin: a1.5rem 0;
  font-size: 1.1rem;
  line-height: 1.5;
  padding: 0 1rem;
}

.welcome-message p {
  margin-bottom: 1rem;
}

.form-subtitle {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.5rem;
  color: #4a6741; /* Sage green */
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.input-field {
  width: 100%;
  padding: 0.8rem;
  margin-bottom: 1rem;
  border: 1px solid #a3b18a; /* Soft sage green */
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1rem;
  transition: border-color 0.3s;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
}

.input-field:focus {
  outline: none;
  border-color: #4a6741; /* Sage green */
  box-shadow: 0 0 5px rgba(74, 103, 65, 0.3);
}

.submit-button {
  width: 100%;
  padding: 0.875rem;
  background: #4a6741; /* Sage green */
  color: white;
  border: none;
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  -webkit-appearance: none;
  appearance: none;
}

.submit-button:hover {
  background: #3d5635; /* Darker sage green */
  transform: translateY(-1px);
}

.thank-you-message {
  margin-top: 1.5rem;  /* Added margin to separate from bank details */
  text-align: center;
  font-family: 'Cormorant Garamond', serif;
  color: #4a6741;
  font-size: 1.2rem;
}

/* Dropdown styling */
select.input-field {
  background: white;
  color: #4a6741; /* Sage green */
  cursor: pointer;
}

/* Success Modal Styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.success-modal {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  max-width: 90%;
  width: 400px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  position: relative;
  animation: slideUp 0.4s ease-out;
  border: 2px solid #d1d9c3;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-icon {
  width: 70px;
  height: 70px;
  background-color: #4a6741;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(74, 103, 65, 0.3);
}

.modal-title {
  font-family: 'Great Vibes', cursive;
  font-size: 2.2rem;
  color: #3d5635;
  margin-bottom: 1rem;
}

.modal-message {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.modal-button {
  margin-top: 1.5rem;
  padding: 0.7rem 2rem;
  background: #4a6741;
  color: white;
  border: none;
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.modal-button:hover {
  background: #3d5635;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 103, 65, 0.3);
}

/* Bank details styling */
.bank-details-section {
  margin: 2rem 0 1rem 0;  /* Adjusted margins */
  padding: 1.5rem;
  background: rgba(74, 103, 65, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(74, 103, 65, 0.2);
}

.support-title {
  font-family: 'Great Vibes', cursive;
  color: #4a6741;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

.support-message {
  font-family: 'Cormorant Garamond', serif;
  color: #555;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  line-height: 1.4;
  text-align: center;
}

.section-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #4a6741, transparent);
  margin: 2rem 0;
  opacity: 0.3;
}

.bank-details-card {
  background: white;
  border: 1px solid rgba(74, 103, 65, 0.3);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.bank-details-text {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1rem;
  color: #333;
  white-space: pre-wrap;
  margin: 0;
  padding: 0;
  line-height: 1.5;
  font-weight: 500;
  user-select: all;
}

.copy-button {
  display: block;
  margin: 1rem auto 0;
  padding: 0.5rem 1rem;
  background: #a3b18a;
  color: white;
  border: none;
  border-radius: 4px;
  font-family: 'Cormorant Garamond', serif;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.copy-button:hover {
  background: #4a6741;
}

/* Error Modal Styling */
.error-modal {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  max-width: 90%;
  width: 400px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  text-align: center;
  position: relative;
  animation: slideUp 0.4s ease-out;
  border: 2px solid #f8d7da; /* Light red border */
}

.error-modal .modal-icon {
  width: 70px;
  height: 70px;
  background-color: #dc3545; /* Bootstrap danger red */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.error-modal .modal-title {
  font-family: 'Great Vibes', cursive;
  font-size: 2.2rem;
  color: #721c24; /* Dark red */
  margin-bottom: 1rem;
}

.error-modal .modal-message {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.1rem;
  color: #721c24; /* Dark red for better readability */
  margin-bottom: 0.5rem;
  line-height: 1.4;
  font-weight: 500;
}

.error-modal .modal-button {
  margin-top: 1.5rem;
  padding: 0.7rem 2rem;
  background: #dc3545; /* Bootstrap danger red */
  color: white;
  border: none;
  border-radius: 8px;
  font-family: 'Cormorant Garamond', serif;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.error-modal .modal-button:hover {
  background: #c82333; /* Darker red on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.modal-icon.error {
  background-color: #dc3545;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Mobile-specific adjustments */
@media (max-width: 600px) {
  .guest-form-container {
    padding: 0.5rem;
  }
  
  .form-card {
    padding: 1.25rem;
    margin: 0.25rem;
    border-radius: 10px;
  }
  
  .input-field {
    padding: 0.7rem;
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
  
  .submit-button {
    padding: 0.75rem;
    font-size: 1rem;
  }
  
  .success-modal,
  .error-modal {
    width: 85%;
    padding: 1.5rem;
  }

  .modal-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .modal-title {
    font-size: 1.8rem;
  }
  
  .bank-details-card {
    padding: 0.75rem;
  }
  
  .bank-details-text {
    font-size: 0.9rem;
  }
}

/* Fix for iOS input shadows */
@supports (-webkit-touch-callout: none) {
  .input-field {
    background-color: #ffffff;
  }
}

/* Mobile responsiveness */
@media (max-width: 600px) {
  .bank-details-section {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .support-title {
    font-size: 1.5rem;
  }

  .support-message {
    font-size: 1rem;
  }
}
