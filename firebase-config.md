# Firebase Configuration

## Overview
Secure Firebase configuration implementation for the Wedding Registry application, utilizing environment variables and modern Firebase SDK.

## Key Features
- Environment-based configuration
- Secure credential management
- Modular Firebase initialization
- Clean separation of concerns

## Implementation
The Firebase configuration is managed through environment variables:

```javascript
const firebaseConfig = {
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APP_ID,
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
};
```

## Setup Instructions
1. Create a `.env` file in your project root
2. Add your Firebase credentials using the following format:
   ```
   REACT_APP_FIREBASE_API_KEY=your_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
   REACT_APP_FIREBASE_PROJECT_ID=your_project_id
   REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   REACT_APP_FIREBASE_APP_ID=your_app_id
   REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id
   ```
3. Ensure `.env` is listed in `.gitignore`

## Security Considerations
- Never commit `.env` files to version control
- Rotate credentials if accidentally exposed
- Use appropriate Firebase security rules
- Implement proper error handling

## Usage
```javascript
import { db } from './firebase';

// Use the db instance for Firestore operations
```