# Wedding Guest Registration Form

## Overview

A React-based web form for registering wedding guests, integrated with Firebase Firestore for seamless data collection and management.

## Features

- Responsive, mobile-friendly design
- Real-time guest data submission to Firestore
- Validation for all input fields
- Dropdown selection for guest affiliation
- Elegant, wedding-themed styling

## Component Structure

### State Management
- Uses React `useState` hook for form data management
- Initial state includes:
  - `name`: Guest's first name
  - `surname`: Guest's last name
  - `email`: Guest's email address
  - `phone`: Guest's phone number
  - `behalfOf`: Guest's affiliation (default: Onesmus)

### Form Handling
- `handleChange`: Updates form state dynamically
- `handleSubmit`: 
  - Prevents default form submission
  - Checks for existing Firestore document
  - Creates or updates document with new guest data
  - Resets form after successful submission
  - Displays success/error alerts

## Firebase Integration

### Configuration
- Uses environment variables for Firebase credentials
- Connects to Firestore database
- Supports document creation and array updates

### Data Structure in Firestore
```json
{
  "guests": [
    {
      "name": "<PERSON>",
      "surname": "<PERSON><PERSON>",
      "email": "<EMAIL>",
      "phone": "1234567890",
      "behalfOf": "Onesmus"
    }
  ]
}
```

## Styling

### Fonts
- Body: Cormorant Garamond
- Headings: Great Vibes

### Color Palette
- Primary Color: HSL(305, 46%, 51%)
- Background: Soft white with wedding-themed overlay

### Responsive Design
- Mobile-friendly layout
- Adaptive font sizes
- Touch-friendly inputs

## Prerequisites

- React 16.8+ (for Hooks support)
- Firebase project
- Configured `.env` file with Firebase credentials

## Environment Variables

Required in `.env`:
- `REACT_APP_FIREBASE_API_KEY`
- `REACT_APP_FIREBASE_AUTH_DOMAIN`
- `REACT_APP_FIREBASE_PROJECT_ID`
- `REACT_APP_FIREBASE_STORAGE_BUCKET`
- `REACT_APP_FIREBASE_MESSAGING_SENDER_ID`
- `REACT_APP_FIREBASE_APP_ID`

## Installation

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up Firebase project
4. Configure `.env` file
5. Run the application: `npm start`

## Customization

- Modify form fields in the `formData` state
- Adjust styling in `index.css`
- Add/remove dropdown options in `behalfOf` select

## Error Handling

- Client-side input validation
- Required field checks
- Firebase error handling with user-friendly alerts

## Browser Compatibility

- Modern browsers
- iOS and Android support
- Responsive across devices

## Future Improvements

- Add more detailed form validation
- Implement guest list deduplication
- Add optional guest details

## License

Distributed under the MIT License

## Contact

Onesmus Maenzanise - <EMAIL>